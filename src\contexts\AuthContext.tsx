
import React, { createContext, useState, useEffect, useContext } from 'react';
import { auth } from '@/lib/firebase';
import { onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { FirebaseService } from '@/services/firebase';
import { UserProfile } from '@/types/firebase';
import { Timestamp } from 'firebase/firestore';

interface AuthContextProps {
  currentUser: any;
  userProfile: UserProfile | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, displayName: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextProps>({
  currentUser: null,
  userProfile: null,
  loading: true,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const login = async (email: string, password: string) => {
    await signInWithEmailAndPassword(auth, email, password);
  };

  const register = async (email: string, password: string, displayName: string) => {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    
    // Create user profile
    const userProfile = {
      uid: result.user.uid,
      email: result.user.email || '',
      displayName: displayName || result.user.email || '',
      role: 'user' as const,
      preferences: {
        accommodation: 'midrange' as const,
        activities: [],
        dietaryRestrictions: [],
        fitnessLevel: 'moderate' as const,
        photographyInterest: false,
        birdingInterest: false
      },
      loyaltyPoints: 0,
      pastBookings: [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    await FirebaseService.updateUserProfile(result.user.uid, userProfile);
  };

  const logout = async () => {
    await signOut(auth);
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user);
        
        try {
          const profile = await FirebaseService.getUserProfile(user.uid);
          if (profile) {
            setUserProfile(profile as UserProfile);
          } else {
            // Create user profile if it doesn't exist
            const userProfile = {
              uid: user.uid,
              email: user.email || '',
              displayName: user.displayName || user.email || '',
              role: 'user' as const,
              preferences: {
                accommodation: 'midrange' as const,
                activities: [],
                dietaryRestrictions: [],
                fitnessLevel: 'moderate' as const,
                photographyInterest: false,
                birdingInterest: false
              },
              loyaltyPoints: 0,
              pastBookings: [],
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now()
            };

            await FirebaseService.updateUserProfile(user.uid, userProfile);
            setUserProfile(userProfile);
          }
        } catch (error) {
          console.error('Error loading user profile:', error);
        }
      } else {
        setCurrentUser(null);
        setUserProfile(null);
      }
      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextProps = { 
    currentUser, 
    userProfile, 
    loading, 
    login, 
    register, 
    logout 
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
